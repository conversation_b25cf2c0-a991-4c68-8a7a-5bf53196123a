import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createClient } from '@supabase/supabase-js'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16'
})

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(req: NextRequest) {
  const body = await req.text()
  const sig = req.headers.get('stripe-signature')!

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Invalid webhook signature'
    return NextResponse.json(
      { error: `Webhook Error: ${errorMessage}` },
      { status: 400 }
    )
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session
        
        if (session.mode === 'subscription') {
          const subscription = await stripe.subscriptions.retrieve(
            session.subscription as string
          )
          
          const plan = subscription.items.data[0].price.lookup_key as 'pro' | 'team'
          const limits = plan === 'pro' ? 500 : 5000
          
          await supabase
            .from('profiles')
            .update({
              plan,
              queries_limit: limits,
              stripe_customer_id: session.customer as string,
              stripe_subscription_id: subscription.id
            })
            .eq('id', session.client_reference_id!)
        }
        break

      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        const subscription = event.data.object as Stripe.Subscription
        
        if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
          // Downgrade to free plan
          const { data: profile } = await supabase
            .from('profiles')
            .select('id')
            .eq('stripe_subscription_id', subscription.id)
            .single()
          
          if (profile) {
            await supabase
              .from('profiles')
              .update({
                plan: 'free',
                queries_limit: 10,
                stripe_subscription_id: null
              })
              .eq('id', profile.id)
          }
        }
        break
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}