{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(pnpm install:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(tree:*)", "WebFetch(domain:supabase.com)", "<PERSON><PERSON>(touch:*)", "Bash(pnpm dev:*)", "Bash(pnpm add:*)", "Bash(sudo pnpm install:*)", "Bash(pnpm build:*)", "Bash(pnpm --filter @excelairate/excel-addin build)", "Bash(pnpm --filter shared check-types)", "<PERSON><PERSON>(true)", "Bash(npx tsc:*)", "Bash(pnpm run:*)", "Bash(pnpm -w run build)", "Bash(npx vite:*)", "Bash(node:*)", "Bash(cp:*)", "Bash(./node_modules/.bin/vite:*)", "Bash(git rm:*)", "Bash(grep:*)", "Bash(pnpm check-types:*)", "Bash(pnpm tsc:*)", "Bash(pnpm:*)", "Bash(npm view:*)", "Bash(rg:*)", "Bash(ln:*)"], "deny": []}}