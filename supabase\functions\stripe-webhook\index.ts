import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.0.0'
import { getCorsHeaders, corsResponse } from '../_shared/cors.ts'

const PLAN_LIMITS = {
  free: { queries: 10 },
  pro: { queries: 500 },
  team: { queries: 5000 }
}

serve(async (req) => {
  const origin = req.headers.get('origin')
  
  // Handle OPTIONS request for CORS
  if (req.method === 'OPTIONS') {
    return corsResponse(origin)
  }
  
  try {
    // Initialize services
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const stripeKey = Deno.env.get('STRIPE_SECRET_KEY')!
    const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    const stripe = new Stripe(stripeKey, { apiVersion: '2023-10-16' })

    // Verify webhook signature
    const signature = req.headers.get('stripe-signature')!
    const body = await req.text()
    
    let event: Stripe.Event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
    } catch (err) {
      return new Response('Webhook signature verification failed', { 
        status: 400,
        headers: getCorsHeaders(origin)
      })
    }

    // Handle events
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        const userId = subscription.metadata.user_id
        const plan = subscription.metadata.plan || 'pro'
        
        await supabase
          .from('profiles')
          .update({
            plan,
            stripe_subscription_id: subscription.id,
            subscription_status: subscription.status,
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            queries_limit: PLAN_LIMITS[plan].queries,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
        
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        const userId = subscription.metadata.user_id
        
        await supabase
          .from('profiles')
          .update({
            plan: 'free',
            stripe_subscription_id: null,
            subscription_status: 'canceled',
            current_period_end: null,
            queries_limit: PLAN_LIMITS.free.queries,
            queries_used: 0, // Reset usage
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
        
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
        const userId = subscription.metadata.user_id
        
        // Reset usage on successful payment
        await supabase
          .from('profiles')
          .update({
            queries_used: 0,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
        
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string)
        const userId = subscription.metadata.user_id
        
        await supabase
          .from('profiles')
          .update({
            subscription_status: 'past_due',
            updated_at: new Date().toISOString()
          })
          .eq('id', userId)
        
        break
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...getCorsHeaders(origin), 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...getCorsHeaders(origin), 'Content-Type': 'application/json' } }
    )
  }
})