{"name": "@excelairate/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@stripe/stripe-js": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.8.0", "@supabase/supabase-js": "^2.39.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^14.0.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.0", "eslint": "^9", "eslint-config-next": "15.3.5", "@eslint/eslintrc": "^3", "postcss": "^8.4.0", "tailwindcss": "^4", "typescript": "^5"}}