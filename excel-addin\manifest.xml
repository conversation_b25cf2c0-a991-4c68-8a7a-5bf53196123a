<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0"
  xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides"
  xsi:type="TaskPaneApp">
  
  <!-- Begin Basic Settings: Add-in metadata, used for all versions of Office unless override provided. -->
  <Id>a8e4b7c5-3d2f-4e1a-9f8b-1c2d3e4f5a6b</Id>
  <Version>*******</Version>
  <ProviderName>ExcelAIRate</ProviderName>
  <DefaultLocale>en-US</DefaultLocale>
  
  <DisplayName DefaultValue="AI Assistant for Excel"/>
  <Description DefaultValue="Transform your Excel data with AI-powered analysis and content generation"/>
  
  <IconUrl DefaultValue="https://localhost:5173/assets/icon-32.png"/>
  <HighResolutionIconUrl DefaultValue="https://localhost:5173/assets/icon-64.png"/>
  <SupportUrl DefaultValue="https://excelairate.com/support"/>
  
  <AppDomains>
    <AppDomain>https://excelairate.com</AppDomain>
    <AppDomain>https://localhost:5173</AppDomain>
  </AppDomains>
  
  <Hosts>
    <Host Name="Workbook"/>
  </Hosts>
  
  <Requirements>
    <Sets DefaultMinVersion="1.1">
      <Set Name="SharedRuntime" MinVersion="1.1"/>
    </Sets>
  </Requirements>
  
  <DefaultSettings>
    <SourceLocation DefaultValue="https://localhost:5173/index.html"/>
  </DefaultSettings>
  
  <Permissions>ReadWriteDocument</Permissions>
  
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">
    <Hosts>
      <Host xsi:type="Workbook">
        <Runtimes>
          <Runtime resid="Taskpane.Runtime" lifetime="long" />
        </Runtimes>
        
        <DesktopFormFactor>
          <GetStarted>
            <Title resid="GetStarted.Title"/>
            <Description resid="GetStarted.Description"/>
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
          </GetStarted>
          
          <FunctionFile resid="Taskpane.Url"/>
          
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <OfficeTab id="TabHome">
              <Group id="CommandsGroup">
                <Label resid="CommandsGroup.Label"/>
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16"/>
                  <bt:Image size="32" resid="Icon.32x32"/>
                  <bt:Image size="80" resid="Icon.80x80"/>
                </Icon>
                
                <Control xsi:type="Button" id="TaskpaneButton">
                  <Label resid="TaskpaneButton.Label"/>
                  <Supertip>
                    <Title resid="TaskpaneButton.Label"/>
                    <Description resid="TaskpaneButton.Tooltip"/>
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16"/>
                    <bt:Image size="32" resid="Icon.32x32"/>
                    <bt:Image size="80" resid="Icon.80x80"/>
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url"/>
                  </Action>
                </Control>
                
                <Control xsi:type="Button" id="QuickAnalyzeButton">
                  <Label resid="QuickAnalyze.Label"/>
                  <Supertip>
                    <Title resid="QuickAnalyze.Label"/>
                    <Description resid="QuickAnalyze.Tooltip"/>
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16.Analysis"/>
                    <bt:Image size="32" resid="Icon.32x32.Analysis"/>
                    <bt:Image size="80" resid="Icon.80x80.Analysis"/>
                  </Icon>
                  <Action xsi:type="ExecuteFunction">
                    <FunctionName>quickAnalyze</FunctionName>
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>
    
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://localhost:5173/assets/icon-16.png"/>
        <bt:Image id="Icon.32x32" DefaultValue="https://localhost:5173/assets/icon-32.png"/>
        <bt:Image id="Icon.80x80" DefaultValue="https://localhost:5173/assets/icon-80.png"/>
        <bt:Image id="Icon.16x16.Analysis" DefaultValue="https://localhost:5173/assets/analysis-16.png"/>
        <bt:Image id="Icon.32x32.Analysis" DefaultValue="https://localhost:5173/assets/analysis-32.png"/>
        <bt:Image id="Icon.80x80.Analysis" DefaultValue="https://localhost:5173/assets/analysis-80.png"/>
      </bt:Images>
      
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://excelairate.com/getting-started"/>
        <bt:Url id="Taskpane.Url" DefaultValue="https://localhost:5173/index.html"/>
        <bt:Url id="Taskpane.Runtime" DefaultValue="https://localhost:5173/runtime.html" />
      </bt:Urls>
      
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="Get started with AI Assistant!"/>
        <bt:String id="CommandsGroup.Label" DefaultValue="AI Assistant"/>
        <bt:String id="TaskpaneButton.Label" DefaultValue="AI Assistant"/>
        <bt:String id="QuickAnalyze.Label" DefaultValue="Quick Analyze"/>
      </bt:ShortStrings>
      
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="Your add-in loaded successfully. Click the AI Assistant button to get started."/>
        <bt:String id="TaskpaneButton.Tooltip" DefaultValue="Open AI Assistant to analyze data and generate content"/>
        <bt:String id="QuickAnalyze.Tooltip" DefaultValue="Quickly analyze selected data with AI"/>
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>