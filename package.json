{"name": "excelairate", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "clean": "turbo run clean && rm -rf node_modules"}, "dependencies": {"@supabase/supabase-js": "^2.47.10", "@types/node": "^22.10.2"}, "devDependencies": {"prettier": "^3.6.0", "turbo": "^2.5.4", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}