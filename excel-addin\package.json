{"name": "@excelairate/excel-addin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "typecheck": "tsc --noEmit", "preview": "vite preview", "office:validate": "office-addin-manifest validate manifest.xml", "office:start": "office-addin-debugging start manifest.xml desktop --dev-server-port 5173"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "@types/office-js": "^1.0.514", "@microsoft/office-js": "^1.1.110", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "office-addin-cli": "^1.5.0", "office-addin-debugging": "^5.0.0", "office-addin-manifest": "^1.12.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "vite": "^5.0.0"}}